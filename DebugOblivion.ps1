# DebugOblivion.ps1
# Usage: PowerShell.exe -NoProfile -ExecutionPolicy Bypass -File "C:\Scripts\DebugOblivion.ps1"

# Suppress profile and history errors
$ErrorActionPreference = 'SilentlyContinue'

# Ensure the log directory exists
$logDir = 'C:\Temp'
if (-not (Test-Path $logDir)) {
    New-Item -Path $logDir -ItemType Directory -Force | Out-Null
}

# Build a timestamped log file and pre-create it
$timestamp = Get-Date -Format 'yyyyMMdd_HHmmss'
$log = "${logDir}\HealthCheck_${timestamp}.log"
New-Item -Path $log -ItemType File -Force | Out-Null

# Helper function for writing lines to the log
function Write-Log ($Text) {
    $ts = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    Add-Content -Path $log -Value "[$ts] $Text"
}

Write-Log '=== Starting system health check ==='

# System integrity checks
Write-Log 'Running DISM /Online /Cleanup-Image /RestoreHealth'
DISM /Online /Cleanup-Image /RestoreHealth | ForEach-Object { Write-Log $_ }

Write-Log 'Running SFC /scannow'
sfc /scannow | ForEach-Object { Write-Log $_ }

Write-Log 'Running CHKDSK C: (read-only)'
chkdsk C: | ForEach-Object { Write-Log $_ }

# Crash dump analysis (if WinDbg is installed)
$dumpFolder = "$env:LOCALAPPDATA\CrashDumps"
Write-Log "Scanning for .dmp files in $dumpFolder"
if (Get-Command windbg.exe -ErrorAction SilentlyContinue) {
    $symPath = 'srv*C:\Symbols*https://msdl.microsoft.com/download/symbols'
    Get-ChildItem -Path $dumpFolder -Filter '*.dmp' -ErrorAction SilentlyContinue | ForEach-Object {
        Write-Log "Analyzing dump: $($_.Name)"
        & windbg -y $symPath -z $_.FullName -c '!analyze -v; kv; .exit' 2>&1 | ForEach-Object { Write-Log $_ }
    }
} else {
    Write-Log 'WinDbg not found - skipping dump analysis'
}

# Add game-specific checks
Write-Log '=== Game-specific checks ==='

# Check Oblivion save directory
$oblivionSaves = "$env:USERPROFILE\Documents\My Games\Oblivion\Saves"
Write-Log "Checking Oblivion saves directory: $oblivionSaves"
if (Test-Path $oblivionSaves) {
    $saveFiles = Get-ChildItem -Path $oblivionSaves -Filter "*.ess" -ErrorAction SilentlyContinue
    Write-Log "Found $($saveFiles.Count) save files"
    
    # Check permissions on save directory
    $acl = Get-Acl -Path $oblivionSaves -ErrorAction SilentlyContinue
    Write-Log "Save directory permissions: $($acl.AccessToString)"
} else {
    Write-Log "ERROR: Oblivion saves directory not found!"
}

# Add memory optimization for Oblivion
Write-Log '=== Optimizing system memory for Oblivion ==='

# Check if MMAgent module is available
if (Get-Module -ListAvailable -Name MMAgent -ErrorAction SilentlyContinue) {
    Write-Log "Configuring memory management for gaming performance"
    # Enable memory compression and application prefetching
    try {
        Enable-MMAgent -ApplicationLaunchPrefetching -MemoryCompression -ErrorAction SilentlyContinue
        $mmStatus = Get-MMAgent
        Write-Log "Memory optimization status: Memory Compression = $($mmStatus.MemoryCompression), App Prefetching = $($mmStatus.ApplicationLaunchPrefetching)"
    } catch {
        Write-Log "Failed to optimize memory: $_"
    }
} else {
    Write-Log "MMAgent module not available - skipping memory optimization"
}

# Check Oblivion INI files
$oblivionIni = "$env:USERPROFILE\Documents\My Games\Oblivion\Oblivion.ini"
Write-Log "Checking Oblivion.ini at: $oblivionIni"
if (Test-Path $oblivionIni) {
    # Backup the INI file first
    Copy-Item -Path $oblivionIni -Destination "$oblivionIni.backup" -Force
    Write-Log "Created backup of Oblivion.ini"
    
    # Check for common problematic settings
    $iniContent = Get-Content -Path $oblivionIni -Raw
    if ($iniContent -match "bUseThreadedBlood=1") {
        Write-Log "WARNING: Found problematic setting 'bUseThreadedBlood=1' which can cause crashes"
    }
    
    Write-Log "Oblivion.ini file exists and was checked"
} else {
    Write-Log "ERROR: Oblivion.ini not found!"
}

Write-Log '=== Health check completed ==='
Write-Log "Log file saved at $log"

# Final message
Write-Output ("Done. See results in {0}" -f $log)
